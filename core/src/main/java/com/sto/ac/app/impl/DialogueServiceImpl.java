package com.sto.ac.app.impl;

import com.alibaba.dashscope.app.*;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sto.ac.app.DialogueService;
import com.sto.ac.app.KnowledgeBaseService;
import com.sto.ac.app.dto.DialogueResponseDTO;
import com.sto.ac.app.dto.IntentOutputDTO;
import com.sto.ac.app.dto.KnowledgeBaseResponseDTO;
import com.sto.ac.app.dto.OutputDTO;
import com.sto.ac.common.base.UserInfoResp;
import com.sto.ac.common.enums.ChartTypeEnum;
import com.sto.ac.common.util.UserInfoHolder;
import io.reactivex.Flowable;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class DialogueServiceImpl implements DialogueService {

    @Value("${xihe.dialogue.apiKey:sk-29ab26f17d384119bdb62e1ecd678a18}")
    private String apiKey;

    @Value("${xihe.dialogue.data.appId:cdc0546b1cd8436c87eba9af8d02d305}")
    private String dataQueryAppId;

    @Value("${xihe.dialogue.intent.appId:a7149537bffd43bdbe3f60ce9603ac38}")
    private String intentAppId;

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    @Override
    public void dialogue(String query, String intentSessionId, String dataSessionId, SseEmitter emitter) {
        try {
            // 参数校验
            if (StringUtils.isBlank(query)) {
                log.warn("DialogueServiceImpl#dialogue query is blank");
                emitter.send(SseEmitter.event().data("查询内容不能为空").name("message"));
                emitter.complete();
                return;
            }

            // 先意图识别，使用intentSessionId
            IntentOutputDTO queryIntent = getQueryIntent(query, intentSessionId);
            log.info("DialogueServiceImpl#dialogue query={}, intentSessionId={}, dataSessionId={}, intent={}", 
                    query, intentSessionId, dataSessionId, JSON.toJSONString(queryIntent));
            
            if (Objects.isNull(queryIntent)) {
                log.error("DialogueServiceImpl#dialogue intent recognition failed for query={}", query);
                emitter.send(SseEmitter.event().data("意图识别失败，请稍后重试").name("message"));
                emitter.complete();
                return;
            }

            String intent = queryIntent.getIntent();
            String newIntentSessionId = queryIntent.getSessionId();
            
            // 根据意图进行不同的处理
            if ("2".equals(intent)) {
                // 意图为2：调用业务数据查询Agent（支持多轮对话），使用dataSessionId
                log.info("DialogueServiceImpl#dialogue calling data query agent for query={}, dataSessionId={}", query, dataSessionId);
                queryData(query, dataSessionId, newIntentSessionId, emitter);
            } else {
                // 意图为1或-1：调用外部知识库问答接口（不支持多轮对话）
                log.info("DialogueServiceImpl#dialogue calling knowledge base QA for query={}", query);
                queryQA(query, newIntentSessionId, emitter);
            }
        } catch (Exception e) {
            log.error("DialogueServiceImpl#dialogue unexpected error for query={}, intentSessionId={}, dataSessionId={}", 
                    query, intentSessionId, dataSessionId, e);
            try {
                emitter.send(SseEmitter.event().data("系统异常，请稍后重试"));
                emitter.completeWithError(e);
            } catch (Exception sendException) {
                log.error("DialogueServiceImpl#dialogue failed to send error message", sendException);
                emitter.completeWithError(e);
            }
        }
    }

    private void queryQA(String query, String intentSessionId, SseEmitter emitter) {
        try {
            log.info("DialogueServiceImpl#queryQA starting knowledge base query for: {}", query);
            
            UserInfoResp userInfo = UserInfoHolder.getUserInfo();
            String wtUserId = userInfo.getUserId();
            
            log.debug("DialogueServiceImpl#queryQA user info: wtUserId={}", wtUserId);
            
            // 调用知识库服务
            knowledgeBaseService.queryKnowledgeBase(query, wtUserId, intentSessionId, emitter);
            
        } catch (Exception e) {
            log.error("DialogueServiceImpl#queryQA error for query: {}", query, e);
            try {
                emitter.send(SseEmitter.event().data("知识库查询失败，系统异常").name("message"));
                emitter.completeWithError(e);
            } catch (Exception sendException) {
                log.error("DialogueServiceImpl#queryQA failed to send error message", sendException);
                emitter.completeWithError(e);
            }
        }
    }

    private void queryData(String query, String dataSessionId, String newIntentSessionId, SseEmitter emitter) {
        try {
            log.info("DialogueServiceImpl#queryData starting data query for: {}, sessionId: {}", query, dataSessionId);
            
            UserInfoResp userInfo = UserInfoHolder.getUserInfo();
            String wtUserId = userInfo.getUserId();
            
            log.debug("DialogueServiceImpl#queryData user info: wtUserId={}", wtUserId);

            ApplicationParam param = ApplicationParam.builder()
                                                     .apiKey(apiKey)
                                                     .appId(dataQueryAppId)
                                                     .prompt("工号" + wtUserId + "用户提问：" + query)
                                                     .flowStreamMode(FlowStreamMode.AGENT_FORMAT)
//                                                     .incrementalOutput(true)
                                                     .build();
            if (StringUtils.isNotBlank(dataSessionId)) {
                param.setSessionId(dataSessionId);
            }
            
            Application application = new Application();
            Flowable<ApplicationResult> result = application.streamCall(param);
            
            result.blockingForEach(data -> {
                ApplicationOutput output = data.getOutput();
                String text = output.getText();
                String newDataSessionId = output.getSessionId();
                String finishReason = output.getFinishReason();
                
                KnowledgeBaseResponseDTO knowledgeBaseResponseDTO = createKnowledgeBaseResponse(newIntentSessionId, newDataSessionId, text);

                emitter.send(SseEmitter.event().data(knowledgeBaseResponseDTO).name("message"));
                if (Objects.equals("stop", finishReason)) {
                    emitter.complete();
                }
            });
            
        } catch (ApiException | NoApiKeyException | InputRequiredException e) {
            emitter.completeWithError(e);
        }
    }


    /**
     * 创建知识库响应DTO
     */
    private KnowledgeBaseResponseDTO createKnowledgeBaseResponse(String intentSessionId, String dataSessionId, String text) {
        KnowledgeBaseResponseDTO response = new KnowledgeBaseResponseDTO();
        KnowledgeBaseResponseDTO.DataDTO dataDTO = new KnowledgeBaseResponseDTO.DataDTO();
        List<OutputDTO> outputList = parseTextWithCharts(text);
        dataDTO.setOutputList(outputList);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("intentSessionId", intentSessionId);
        jsonObject.put("dataSessionId", dataSessionId);
        dataDTO.setSessionId(jsonObject.toJSONString());
        response.setData(dataDTO);
        return response;
    }

    /**
     * 解析包含图表标记的文本
     */
    private List<OutputDTO> parseTextWithCharts(String text) {
        List<OutputDTO> outputList = new ArrayList<>();
        
        // 检查是否包含图表标记
//        ChartTypeEnum detectedChartType = detectChartType(text);
        ChartTypeEnum detectedChartType = detectEndTag(text);
        if (detectedChartType == null) {
            // 没有图表标记，直接返回文本
            outputList.add(createTextOutput(text));
            return outputList;
        }
        String graphData;
        String startTag = detectedChartType.getStartTag();
        String endTag = detectedChartType.getEndTag();
        switch (detectedChartType) {
            case TABLE_CHART:
                JSONObject result = new JSONObject();
                String textData = text.substring(0, text.indexOf(startTag));
                outputList.add(createTextOutput(textData));
                graphData = text.substring(text.indexOf(startTag) + startTag.length(), text.indexOf(endTag)).trim();
                String[] split = graphData.split(";");

                for (String s : split) {
                    s = s.trim();
                    if (s.startsWith("d[")) {
                        result.put("dataSource", parseDataSource(s));
                    } else if (s.startsWith("c[")) {
                        result.put("columns", parseColumns(s));
                    }
                }

                // 添加图表输出
                if (!result.isEmpty()) {
                    OutputDTO chartOutput = new OutputDTO();
                    chartOutput.setType("TABLE_CHART");
                    chartOutput.setContent(result.toJSONString());
                    outputList.add(chartOutput);
                }
                break;

            case LINE_CHART:

            case BAR_CHART:
            default:
                // 创建文本输出
                outputList.add(createTextOutput(text));
                break;
        }

        return outputList;
    }

    private JSONArray parseDataSource(String dataPart) {
        String content = dataPart.replaceAll("d\\[|\\]", "");
        String[] items = content.split("\\\\");
        JSONArray dataSource = new JSONArray();

        for (String item : items) {
            String[] properties = item.split(",");
            JSONObject dataItem = new JSONObject();

            for (String property : properties) {
                String[] keyValue = property.split(":");
                if (keyValue.length == 2) {
                    dataItem.put(keyValue[0], keyValue[1]);
                }
            }

            if (!dataItem.isEmpty()) {
                dataSource.add(dataItem);
            }
        }

        return dataSource;
    }

    private JSONArray parseColumns(String columnPart) {
        String content = columnPart.replaceAll("c\\[|\\]", "");
        String[] items = content.split("\\\\");
        JSONArray columns = new JSONArray();

        for (String item : items) {
            String[] properties = item.split(",");
            JSONObject column = new JSONObject();

            for (String property : properties) {
                String[] keyValue = property.split(":");
                if (keyValue.length == 2) {
                    column.put(keyValue[0], keyValue[1]);
                }
            }

            if (!column.isEmpty()) {
                columns.add(column);
            }
        }

        return columns;
    }

    public static void main(String[] args) {
        String text = "12345TABLE_CHART_START d[date:06-10,count:4873\\date:06-11,count:5129\\date:06-12,count:4987\\date:06-13,count:5012\\date:06-14,count:5234\\date:06-15,count:4901]; c[title:日期,dataIndex:date,key:date\\title:工单处理量,dataIndex:count,key:count] TABLE_CHART_END";
        String substring = text.substring(text.indexOf("TABLE_CHART_START") + "TABLE_CHART_START".length(), text.indexOf("TABLE_CHART_END")).trim();
        System.out.println(text.indexOf("TABLE_CHART_START"));
        String substring1 = text.substring(0, text.indexOf("TABLE_CHART_START"));
        System.out.println(substring1);
        JSONObject result = new JSONObject();
        String[] split = substring.split(";");
        for (String s : split) {
            s = s.trim();
            if (s.startsWith("d[")) {
                String s1 = s.replaceAll("d\\[|\\]", "");
                String[] split1 = s1.split("\\\\");
                JSONArray dataSource = new JSONArray();
                for (String string : split1) {
                    String[] split2 = string.split(",");
                    JSONObject jsonObject = new JSONObject();
                    for (String s2 : split2) {
                        String[] split3 = s2.split(":");
                        jsonObject.put(split3[0], split3[1]);
                    }
                    dataSource.add(jsonObject);
                }
                result.put("dataSource", dataSource);
            } else if (s.startsWith("c[")) {
                // c[title:日期,dataIndex:date,key:date\title:工单处理量,dataIndex:count,key:count]
                String s1 = s.replaceAll("c\\[|\\]", "");
                String[] split1 = s1.split("\\\\");
                JSONArray columns = new JSONArray();
                for (String string : split1) {
                    JSONObject jsonObject = new JSONObject();
                    String[] split2 = string.split(",");
                    for (String s2 : split2) {
                        String[] split3 = s2.split(":");
                        jsonObject.put(split3[0], split3[1]);
                    }
                    columns.add(jsonObject);
                }
                result.put("columns", columns);
            }
        }
        System.out.println(result.toJSONString());
    }

    /**
     * 检测文本中的图表类型
     */
    private ChartTypeEnum detectChartType(String text) {
        for (ChartTypeEnum chartType : ChartTypeEnum.values()) {
            if (text.contains(chartType.getStartTag())) {
                return chartType;
            }
        }
        return null;
    }

    private ChartTypeEnum detectEndTag(String text) {
        for (ChartTypeEnum chartType : ChartTypeEnum.values()) {
            if (text.contains(chartType.getEndTag())) {
                return chartType;
            }
        }
        return null;
    }

    /**
     * 解析包含开始和结束标记的文本
     */
    private List<OutputDTO> parseTextWithStartEndTags(String text, String startTag, String endTag, String chartType) {
        List<OutputDTO> outputList = new ArrayList<>();
        
        String[] splitByStart = text.split(startTag);
        if (splitByStart.length >= 2) {
            // 添加开始标记前的文本
            if (StringUtils.isNotBlank(splitByStart[0])) {
                outputList.add(createTextOutput(splitByStart[0]));
            }
            
            String[] splitByEnd = splitByStart[1].split(endTag);
            if (splitByEnd.length >= 1) {
                // 添加图表内容
                outputList.add(createChartOutput(chartType, splitByEnd[0]));
                
                // 添加结束标记后的文本
                if (splitByEnd.length >= 2 && StringUtils.isNotBlank(splitByEnd[1])) {
                    outputList.add(createTextOutput(splitByEnd[1]));
                }
            }
        }
        
        return outputList;
    }

    /**
     * 解析只包含开始标记的文本
     */
    private List<OutputDTO> parseTextWithStartTag(String text, String startTag, String chartType) {
        List<OutputDTO> outputList = new ArrayList<>();
        
        String[] split = text.split(startTag);
        if (split.length >= 2) {
            // 添加开始标记前的文本
            if (StringUtils.isNotBlank(split[0])) {
                outputList.add(createTextOutput(split[0]));
            }
            // 添加图表内容
            outputList.add(createChartOutput(chartType, split[1]));
        } else if (split.length == 1) {
            // 没有找到标记，添加原始文本
            if (StringUtils.isNotBlank(split[0])) {
                outputList.add(createTextOutput(split[0]));
            }
        }
        
        return outputList;
    }

    /**
     * 创建文本输出DTO
     */
    private OutputDTO createTextOutput(String content) {
        OutputDTO outputDTO = new OutputDTO();
        outputDTO.setType("TEXT");
        outputDTO.setContent(content);
        return outputDTO;
    }

    /**
     * 创建图表输出DTO
     */
    private OutputDTO createChartOutput(String chartType, String content) {
        OutputDTO outputDTO = new OutputDTO();
        outputDTO.setType(chartType);
        outputDTO.setContent(content);
        return outputDTO;
    }

    private IntentOutputDTO getQueryIntent(String query, String sessionId) {
        try {
            log.info("DialogueServiceImpl#getQueryIntent starting intent recognition for: {}, sessionId: {}", query, sessionId);
            
            ApplicationParam param = ApplicationParam.builder()
                                                     .apiKey(apiKey)
                                                     .appId(intentAppId)
                                                     .prompt(query)
                                                     .build();
            if (StringUtils.isNotBlank(sessionId)) {
                param.setSessionId(sessionId);
            }
            
            Application application = new Application();
            ApplicationResult result = application.call(param);
            
            String intent = result.getOutput().getText();
            String resultSessionId = result.getOutput().getSessionId();

            IntentOutputDTO intentOutputDTO = new IntentOutputDTO();
            intentOutputDTO.setIntent(intent);
            intentOutputDTO.setSessionId(resultSessionId);
            
            return intentOutputDTO;
        } catch (ApiException | NoApiKeyException | InputRequiredException e) {
            log.error("DialogueServiceImpl#getQueryIntent 调用百炼羲和意图识别Agent失败 query={}, sessionId={}: {}", query, sessionId, e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("DialogueServiceImpl#getQueryIntent unexpected error for query={}, sessionId={}", query, sessionId, e);
            return null;
        }
    }
}
