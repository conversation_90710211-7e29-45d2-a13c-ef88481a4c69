package com.sto.ac.app.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class DialogueServiceImplTest {

    @InjectMocks
    private DialogueServiceImpl dialogueService;

    @Test
    void testParseBarChartDataSource() throws Exception {
        String dataPart = "d[date:06-10,rate:100.00\\date:06-11,rate:100.00\\date:06-12,rate:99.85]";
        
        Method method = DialogueServiceImpl.class.getDeclaredMethod("parseBarChartDataSource", String.class);
        method.setAccessible(true);
        
        List<JSONObject> result = (List<JSONObject>) method.invoke(dialogueService, dataPart);
        
        assertNotNull(result);
        assertEquals(3, result.size());
        
        JSONObject firstItem = result.get(0);
        assertEquals("06-10", firstItem.getString("category"));
        assertEquals(100.00, firstItem.getDouble("value"), 0.01);
        
        JSONObject thirdItem = result.get(2);
        assertEquals("06-12", thirdItem.getString("category"));
        assertEquals(99.85, thirdItem.getDouble("value"), 0.01);
        
        System.out.println("柱状图数据源解析结果: " + JSON.toJSONString(result));
    }

    @Test
    void testParseXAxis() throws Exception {
        String xPart = "x[field:date]";
        
        Method method = DialogueServiceImpl.class.getDeclaredMethod("parseXAxis", String.class);
        method.setAccessible(true);
        
        JSONObject result = (JSONObject) method.invoke(dialogueService, xPart);
        
        assertNotNull(result);
        assertEquals("date", result.getString("field"));
        
        System.out.println("X轴解析结果: " + result.toJSONString());
    }

    @Test
    void testParseSeries() throws Exception {
        String sPart = "s[field:rate,name:完结率,type:bar,unit:%]";
        
        Method method = DialogueServiceImpl.class.getDeclaredMethod("parseSeries", String.class);
        method.setAccessible(true);
        
        List<JSONObject> result = (List<JSONObject>) method.invoke(dialogueService, sPart);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        
        JSONObject seriesItem = result.get(0);
        assertEquals("rate", seriesItem.getString("field"));
        assertEquals("完结率", seriesItem.getString("name"));
        assertEquals("bar", seriesItem.getString("type"));
        assertEquals("%", seriesItem.getString("unit"));
        
        System.out.println("系列解析结果: " + JSON.toJSONString(result));
    }

    @Test
    void testCompleteBarChartParsing() {
        // 模拟完整的柱状图解析流程
        String testText = "BAR_CHART_START d[date:06-10,rate:100.00\\date:06-11,rate:100.00\\date:06-12,rate:99.85\\date:06-13,rate:100.00\\date:06-14,rate:100.00\\date:06-15,rate:100.00]; x[field:date]; s[field:rate,name:完结率,type:bar,unit:%] BAR_CHART_END";
        
        // 提取图表内容
        String startTag = "BAR_CHART_START";
        String endTag = "BAR_CHART_END";
        String graphData = testText.substring(testText.indexOf(startTag) + startTag.length(), testText.indexOf(endTag)).trim();
        
        System.out.println("提取的图表数据: " + graphData);
        
        // 验证数据格式
        assertTrue(graphData.contains("d["));
        assertTrue(graphData.contains("x["));
        assertTrue(graphData.contains("s["));
        
        String[] parts = graphData.split(";");
        assertEquals(3, parts.length);
        
        System.out.println("分割后的部分:");
        for (int i = 0; i < parts.length; i++) {
            System.out.println("Part " + i + ": " + parts[i].trim());
        }
    }
}
