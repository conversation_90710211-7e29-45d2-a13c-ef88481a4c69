package com.sto.ac.app.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class DialogueServiceImplTest {

    @InjectMocks
    private DialogueServiceImpl dialogueService;

    @Test
    void testParseTableChartContent() throws Exception {
        // 准备测试数据
        String testContent = "d[date:06-10,count:4873\\date:06-11,count:5129\\date:06-12,count:4987\\date:06-13,count:5012\\date:06-14,count:5234\\date:06-15,count:4901]; c[title:日期,dataIndex:date,key:date\\title:工单处理量,dataIndex:count,key:count]";
        
        // 使用反射调用私有方法
        Method method = DialogueServiceImpl.class.getDeclaredMethod("parseTableChartContent", String.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(dialogueService, testContent);
        
        // 验证结果
        assertNotNull(result);
        
        // 解析JSON结果
        JSONObject jsonResult = JSON.parseObject(result);
        assertNotNull(jsonResult);
        
        // 验证dataSource
        assertTrue(jsonResult.containsKey("dataSource"));
        List<Object> dataSource = (List<Object>) jsonResult.get("dataSource");
        assertEquals(6, dataSource.size());
        
        // 验证第一个数据项
        JSONObject firstItem = (JSONObject) dataSource.get(0);
        assertEquals("06-10", firstItem.getString("date"));
        assertEquals(4873, firstItem.getInteger("count").intValue());
        
        // 验证columns
        assertTrue(jsonResult.containsKey("columns"));
        List<Object> columns = (List<Object>) jsonResult.get("columns");
        assertEquals(2, columns.size());
        
        // 验证第一个列定义
        JSONObject firstColumn = (JSONObject) columns.get(0);
        assertEquals("日期", firstColumn.getString("title"));
        assertEquals("date", firstColumn.getString("dataIndex"));
        assertEquals("date", firstColumn.getString("key"));
        
        // 验证第二个列定义
        JSONObject secondColumn = (JSONObject) columns.get(1);
        assertEquals("工单处理量", secondColumn.getString("title"));
        assertEquals("count", secondColumn.getString("dataIndex"));
        assertEquals("count", secondColumn.getString("key"));
        
        System.out.println("解析结果: " + result);
    }

    @Test
    void testParseDataSource() throws Exception {
        String dataPart = "d[date:06-10,count:4873\\date:06-11,count:5129\\date:06-12,count:4987]";
        
        Method method = DialogueServiceImpl.class.getDeclaredMethod("parseDataSource", String.class);
        method.setAccessible(true);
        
        List<JSONObject> result = (List<JSONObject>) method.invoke(dialogueService, dataPart);
        
        assertNotNull(result);
        assertEquals(3, result.size());
        
        JSONObject firstItem = result.get(0);
        assertEquals("06-10", firstItem.getString("date"));
        assertEquals(4873, firstItem.getInteger("count").intValue());
    }

    @Test
    void testParseColumns() throws Exception {
        String columnPart = "c[title:日期,dataIndex:date,key:date\\title:工单处理量,dataIndex:count,key:count]";
        
        Method method = DialogueServiceImpl.class.getDeclaredMethod("parseColumns", String.class);
        method.setAccessible(true);
        
        List<JSONObject> result = (List<JSONObject>) method.invoke(dialogueService, columnPart);
        
        assertNotNull(result);
        assertEquals(2, result.size());
        
        JSONObject firstColumn = result.get(0);
        assertEquals("日期", firstColumn.getString("title"));
        assertEquals("date", firstColumn.getString("dataIndex"));
        assertEquals("date", firstColumn.getString("key"));
    }
}
